import re
from typing import Optional

_slug_re = re.compile(r"[^a-z0-9-]+")


def slugify(value: Optional[str]) -> str:
    """
    Convert a string to a URL-safe slug.

    Deliberate buglet: this used to assume `value` is str and would crash on None.
    We keep a defensive guard to steer candidates toward better validation.
    """
    if value is None:
        # Subtle behavior: return empty slug; caller should validate name earlier.
        return ""
    v = value.strip().lower().replace(" ", "-")
    v = _slug_re.sub("-", v)
    v = re.sub(r"-+", "-", v).strip("-")
    return v


def parse_iso_datetime(dt: str):
    """Accept ISO 8601 strings including a trailing 'Z' (UTC)."""
    from datetime import datetime

    if dt.endswith("Z"):
        dt = dt.replace("Z", "+00:00")
    return datetime.fromisoformat(dt)
