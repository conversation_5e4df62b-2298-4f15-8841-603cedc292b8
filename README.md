# Amp Assist Tasks API — Live Coding Exercise

**Goal:** In ~45 minutes, add two features and fix one bug in a small FastAPI project.

## Setup
1. Clone the repo
2. Python 3.10+ recommended
3. Create and activate a virtualenv
   ```bash
   python -m venv .venv
   source .venv/bin/activate  # Windows: .venv\Scripts\activate
   ```

## Tasks

### Task 1 (Feature 1)

Add filtering to `GET /tasks`:

- Support `status` filter (exact match on `todo|doing|done`).
- Support `q` for case-insensitive name search.
- Return a stable sort by `created_at` ascending.
- Note that the test for this particular task was already added (`test_list_filtering_and_sorting`)


### Task 2 (Feature 2)

Enhance `POST /tasks` to validate:

- **Unique task name** (case-insensitive) across existing tasks.
- **due_date** (optional) must be in the **future**.
- Accept ISO 8601 timestamps including a trailing `Z` (UTC).
- On validation errors, return JSO<PERSON> with a helpful message and status **400** (or 422 if using FastAPI’s validation).
- Note that you will need also to implement the tests for this one (`test_create_validation_and_due_date_z_suffix`)


### Task 3 (Missing request parameter test)

- Posting without a `name` shall return a 422 error that is given by free by FastAPI.
- Complete the test `test_missing_name_returns_client_error_422` to assert that is indeed the case
