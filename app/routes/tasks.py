from datetime import datetime, timezone
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, HTTPException, Query, status
from pydantic import BaseModel, Field

from app import store
from app.utils import parse_iso_datetime, slugify

router = APIRouter()


# Pydantic schemas
class TaskIn(BaseModel):
    name: str = Field(..., min_length=1)
    status: str = Field("todo", pattern=r"^(todo|doing|done)$")
    due_date: Optional[str] = None  # ISO8601 string; optional


class TaskOut(BaseModel):
    id: int
    name: str
    slug: str
    created_at: datetime
    meta: Dict[str, Any]


@router.get("/tasks", response_model=List[TaskOut])
async def list_tasks(
    status: Optional[str] = Query(None, pattern=r"^(todo|doing|done)$"),
    q: Optional[str] = Query(None),
):
    """List tasks.

    TODO (Feature 1):
    - Filter by `status` when provided (task meta is nested under task['meta']['status']).
    - Filter by `q` (case-insensitive substring match on name).
    - Return sorted by `created_at` ASC.
    Note: The test (test_list_filtering_and_sorting) is already written for this function.
    """
    tasks = store.all_tasks()
    # STARTER: currently returns everything unsorted
    return tasks


@router.post("/tasks", response_model=TaskOut, status_code=status.HTTP_201_CREATED)
async def create_task(payload: TaskIn):
    """Create a task with validation.

    TODO (Feature 2):
    - Name must be unique (case-insensitive) across existing tasks.
    - due_date, if provided, must be in the future (UTC).
    - Accept ISO 8601 with a trailing 'Z'.
    - On validation error, raise HTTPException with status 400 (or rely on 422).

    Bugfix: Ensure no crash when name is missing (handled by schema or explicit check).
    Ensure also that the test (test_create_validation_and_due_date_z_suffix) passes for this function.
    """
    name = payload.name
    status_value = payload.status
    due = payload.due_date

    # Uniqueness check (case-insensitive)

    # due_date in future (if provided)
    pass


@router.get("/tasks/{slug}", response_model=TaskOut)
async def get_task(slug: str):
    t = store.find_by_slug(slug)
    if not t:
        raise HTTPException(status_code=404, detail="task not found")
    return t
