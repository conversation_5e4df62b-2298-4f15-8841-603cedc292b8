from __future__ import annotations

from datetime import datetime, timezone
from typing import Dict, List

Task = Dict[str, object]

# In-memory store. Consider this a fake DB.
_TASKS: List[Task] = [
    {
        "id": 1,
        "name": "Write spec",
        "slug": "write-spec",
        "created_at": datetime(2025, 1, 10, 9, 0, tzinfo=timezone.utc),
        "meta": {"status": "todo"},
    },
    {
        "id": 2,
        "name": "Buy milk",
        "slug": "buy-milk",
        "created_at": datetime(2025, 3, 1, 12, 0, tzinfo=timezone.utc),
        "meta": {"status": "done"},
    },
]
_next_id = 3


def all_tasks() -> List[Task]:
    return list(_TASKS)


def add_task(task: Task) -> Task:
    global _next_id
    task = dict(task)
    task["id"] = _next_id
    _next_id += 1
    _TASKS.append(task)
    return task


def find_by_slug(slug: str) -> Task | None:
    return next((t for t in _TASKS if t.get("slug") == slug), None)
