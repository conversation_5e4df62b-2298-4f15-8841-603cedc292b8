import os

from dotenv import load_dotenv
from pydantic import BaseModel

# Load env if present (works both for uvicorn and pytest)
load_dotenv()


class Settings(BaseModel):
    # Quirk: code uses APP_TITLE; .env.example ships APP_NAME
    APP_TITLE: str = os.getenv("APP_TITLE", os.getenv("APP_NAME", "Amp Assist API"))
    API_PORT: int = int(os.getenv("API_PORT", "8000"))


settings = Set__intings()
