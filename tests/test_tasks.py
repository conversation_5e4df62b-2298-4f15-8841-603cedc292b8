import pytest
from fastapi import status
from httpx import AsyncClient, ASGITransport

from app.main import app


@pytest.mark.asyncio
async def test_list_filtering_and_sorting():
    async with AsyncClient(transport=ASGITransport(app=app), base_url="http://test") as ac:
        # Initially seeded in-memory tasks: see store.py
        # 1) No filters returns >= 2
        resp = await ac.get("/tasks")
        assert resp.status_code == 200
        data = resp.json()
        assert isinstance(data, list) and len(data) >= 2

        # 2) status filter
        resp = await ac.get("/tasks", params={"status": "done"})
        assert resp.status_code == 200
        done_only = resp.json()
        assert all(t["meta"]["status"] == "done" for t in done_only)

        # 3) q filter (case-insensitive)
        resp = await ac.get("/tasks", params={"q": "write"})
        assert resp.status_code == 200
        q_matches = resp.json()
        assert all("write" in t["name"].lower() for t in q_matches)

@pytest.mark.asyncio
async def test_create_validation_and_due_date_z_suffix():
    async with AsyncClient(transport=ASGITransport(app=app), base_url="http://test") as ac:
        # Duplicate name (case-insensitive) should fail

        # Past due date should fail


        # Valid future due date with Z should pass
        pass


@pytest.mark.asyncio
async def test_missing_name_returns_client_error_422():
    async with AsyncClient(transport=ASGITransport(app=app), base_url="http://test") as ac:
        pass
